package com.yxt.talent.rv.controller.manage.xpd.rule;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.rule.XpdRuleConfAppService;
import com.yxt.talent.rv.application.xpd.rule.RuleConfComponent;
import com.yxt.talent.rv.controller.manage.xpd.rule.command.*;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimLevelTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.XpdConfResultTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.viewobj.XpdDim4Get;
import com.yxt.talent.rv.controller.manage.xpd.rule.viewobj.XpdDimVo;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.yxt.common.enums.AuthType.DETAILTOKEN;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 * @date 2024/12/16 19:55
 */
@Slf4j
@RestController
@RequestMapping("/xpd/ruleconf")
@RequiredArgsConstructor
@Tag(name = "xpd-管理端-项目规则配置", description = "项目规则配置")
public class XpdRuleConfAppController {

    private final AuthService authService;
    private final XpdRuleConfAppService xpdRuleConfAppService;
    private final RuleConfComponent ruleConfComponent;

    @Operation(summary = "测试-清空规则", hidden = true)
    @PutMapping(value = "/{xpdId}/clearall/test")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void clearRuleConfByModelId(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        ruleConfComponent.clearRuleConfByModelId(userCache.getOrgId(), userCache.getUserId(), xpdId, null);
    }

    @Operation(summary = "详细规则-获取-树形结构")
    @GetMapping(value = "/{xpdId}/detail/tree", produces = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public XpdRuleDetailInfoDto getXpdRuleDetailInfo(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return xpdRuleConfAppService.getXpdRuleDetailInfo(userCache.getOrgId(), xpdId);
    }

    @Operation(summary = "全局配置规则-获取")
    @GetMapping(value = "/{xpdId}/globalinfo", produces = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public XpdRuleConfInfoOutDto getXpdRuleConfInfo(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return xpdRuleConfAppService.getXpdRuleConfInfo(userCache.getOrgId(), xpdId);
    }

    @Operation(summary = "全局配置规则-创建")
    @PostMapping(value = "/{xpdId}/global/create", consumes = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_RULECONFIG_CREATE, paramExp = "#confDto.buildForAudit(#xpdId)")
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public String initXpdRuleConfGlobal(
        HttpServletRequest request, @PathVariable("xpdId") String xpdId,
        @RequestBody XpdRuleConfDto confDto) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        confDto.setXpdId(xpdId);
        return xpdRuleConfAppService.createXpdRuleConfGlobal(userCache.getOrgId(), userCache.getUserId(), confDto);
    }

    @Operation(summary = "解锁-测试", hidden = true)
    @PutMapping(value = "/{xpdId}/key/unlock", consumes = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void unlock(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        xpdRuleConfAppService.unlock(userCache.getOrgId(), xpdId);
    }

    @Operation(summary = "全局配置规则-编辑")
    @PostMapping(value = "/{xpdId}/global/update", consumes = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_RULECONFIG_UPDATE, paramExp = "#xpdId")
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void upXpdRuleConfGlobal(
        HttpServletRequest request, @PathVariable("xpdId") String xpdId,
        @RequestBody XpdRuleConfDto confDto) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        confDto.setXpdId(xpdId);
        xpdRuleConfAppService.upXpdRuleConfGlobal(userCache.getOrgId(), userCache.getUserId(), confDto);
    }

    @Operation(summary = "校验编辑配置规则的变更类型")
    @PostMapping(value = "/{xpdId}/global/modify/check", produces = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public XpdRuleModifyDto checkRuleConfModifyType(
        HttpServletRequest request, @PathVariable("xpdId") String xpdId,
        @RequestBody XpdRuleConfDto confDto) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        confDto.setXpdId(xpdId);
        return xpdRuleConfAppService.checkRuleConfModifyType(userCache.getOrgId(), confDto);
    }

    @Parameter(name = "xpdId", description = "项目ID", in = ParameterIn.PATH)
    @Operation(summary = "快速生成规则-详情")
    @GetMapping(value = "/{xpdId}/fast/detail")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public QuickRule4Get getRuleConfFastInfo(@PathVariable String xpdId) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        XpdRuleConfFastDto confDto = xpdRuleConfAppService.getXpdRuleConfFastInfo(userCacheBasic.getOrgId(), xpdId);
        QuickRule4Get out = new QuickRule4Get();
        out.setLeveltype(String.valueOf(confDto.getLevelType()));
        out.setLevelpriority(String.valueOf(confDto.getLevelPriority()));
        out.setVersion(String.valueOf(confDto.getRuleConfVersion()));
        out.setResulttype(String.valueOf(confDto.getResultType()));
        out.setScoreSystem(confDto.getScoreSystem());
        setChilds(out, confDto);

        return out;
    }

    private void setChilds(QuickRule4Get out, XpdRuleConfFastDto confDto) {
        QuickRuleChild4Get childs = new QuickRuleChild4Get();
        List<QuickLevelRules4Get> quickLevelRules = BeanCopierUtil.convertList(
            confDto.getLevelRuleList(), levelRuleDto -> {
                QuickLevelRules4Get levelRules4Get = new QuickLevelRules4Get();
                levelRules4Get.setLevelname(levelRuleDto.getLevelName());
                levelRules4Get.setId(levelRuleDto.getGridLevelId());
                Optional.ofNullable(levelRuleDto.getBaseValue()).ifPresent(baseValue -> {
                    levelRules4Get.setLevelratio(baseValue.intValue());
                    levelRules4Get.setCompliancerate(baseValue.intValue());
                    levelRules4Get.setScore(baseValue);
                });
                levelRules4Get.setJudgeRule(levelRuleDto.getJudgeRule());
                return levelRules4Get;
            });
        childs.setQuick_level_rules(quickLevelRules);
        out.set_childs(childs);
    }

    @Operation(summary = "快速生成规则-保存")
    @Parameter(name = "xpdId", description = "快速生成规则id", in = ParameterIn.PATH)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_RULECONFIG_EXECCREATE, paramExp = "#bean.buildForAudit(#xpdId)")
    @PutMapping(value = "/{xpdId}/fast/exec", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.TOKEN})
    public void upRuleConfFast(@PathVariable String xpdId, @Validated @RequestBody QuickRule4Update bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        XpdRuleConfFastDto confDto = new XpdRuleConfFastDto();
        confDto.setXpdId(xpdId);
        confDto.setLevelType(Integer.parseInt(bean.getLeveltype()));
        confDto.setLevelPriority(Integer.parseInt(bean.getLevelpriority()));
        confDto.setRuleConfVersion(Integer.parseInt(bean.getVersion()));
        bean.setXpdId(xpdId);
        extractChilds(confDto, bean);
        xpdRuleConfAppService.upRuleConfFast(userCacheBasic.getOrgId(), userCacheBasic.getUserId(), confDto);
    }

    private void extractChilds(XpdRuleConfFastDto confDto, QuickRule4Update bean) {
        QuickRuleChild4Update childs = bean.get_childs();
        if (Objects.nonNull(childs)) {
            Integer levelType = Integer.parseInt(bean.getLeveltype());
            List<QuickLevelRules4Update> quickLevelRules = childs.getQuick_level_rules();
            List<XpdDimLevelRuleDto> dimLevelRuleDtos = BeanCopierUtil.convertList(
                quickLevelRules, level -> {
                    XpdDimLevelRuleDto levelRuleDto = new XpdDimLevelRuleDto();
                    levelRuleDto.setGridLevelId(level.getId());
                    levelRuleDto.setLevelName(level.getLevelname());
                    if (DimLevelTypeEnum.byRatio(levelType)) {
                        levelRuleDto.setBaseValue(BigDecimal.valueOf(level.getLevelratio()));
                    } else if (DimLevelTypeEnum.byFixedValue(levelType)) {
                        if (XpdConfResultTypeEnum.byScore(Integer.parseInt(bean.getResulttype()))) {
                            levelRuleDto.setBaseValue(level.findScoreByScoreSystem(bean.getScoreSystem()));
                        } else {
                            levelRuleDto.setBaseValue(BigDecimal.valueOf(level.getCompliancerate()));
                        }
                    } else {
                        levelRuleDto.setJudgeRule(level.getJudgeRule());
                    }
                    return levelRuleDto;
                });
            confDto.setLevelRuleList(dimLevelRuleDtos);
        }
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY),
        @Parameter(name = "excludePerf", description = "是否过滤绩效维度（0-否 1-是）, 默认否", in = ParameterIn.QUERY),
        @Parameter(name = "onlyRuleDim", description = "是否只返回规则中绑定的维度（0-否 1-是），默认是", in = ParameterIn.QUERY),})
    @Operation(summary = "盘点维度列表")
    @PostMapping(value = "/dim/list", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<XpdDim4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = search.getFilterEq().get("xpdId");
        int excludePerf = Integer.parseInt(search.getFilterEq().getOrDefault("excludePerf", "0"));
        int onlyRuleDim = Integer.parseInt(search.getFilterEq().getOrDefault("onlyRuleDim", "1"));
        // 名称搜索
        String searchKey = search.getSearch().getValue();
        searchKey = SqlUtil.escapeSql(searchKey);

        return xpdRuleConfAppService.getXpdDimPage(request, xpdId, excludePerf, onlyRuleDim, searchKey);
    }

    // 获取项目选择的维度列表
    @Operation(summary = "获取项目选择的维度列表")
    @GetMapping(value = "/{xpdId}/dim/list", produces = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {DETAILTOKEN}, codes = {AUTH_CODE_ALL})
    public List<XpdDimVo> listXpdDim(
        HttpServletRequest request, @PathVariable("xpdId") String xpdId,
        @RequestParam(defaultValue = "0") Integer excludePerf) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return xpdRuleConfAppService.listXpdDim(userCache, xpdId, excludePerf);
    }

    // 获取项目下所有已使用的指标列表
    @Operation(summary = "获取项目下所有已使用的指标列表")
    @GetMapping(value = "/{xpdId}/indicator/used", produces = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {DETAILTOKEN}, codes = {AUTH_CODE_ALL})
    public List<String> listXpdIndicatorUsed(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return xpdRuleConfAppService.listXpdIndicatorUsed(userCache, xpdId);
    }

    @Operation(summary = "检查是否配置规则")
    @GetMapping(value = "/{xpdId}/exists")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public Boolean isXpdRuleExists(HttpServletRequest request, @PathVariable("xpdId") String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return xpdRuleConfAppService.isXpdRuleExists(userCache.getOrgId(), xpdId);
    }

}
