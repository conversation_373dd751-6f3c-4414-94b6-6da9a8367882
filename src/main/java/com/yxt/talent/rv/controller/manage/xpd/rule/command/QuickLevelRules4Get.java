package com.yxt.talent.rv.controller.manage.xpd.rule.command;

import com.yxt.spsdk.common.bean.SpRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "分层规则返回Bean")
public class QuickLevelRules4Get implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "分层ID")
    private String id;

    @Schema(description = "分层名称")
    private String levelname;

    @Schema(description = "人员占比")
    private Integer levelratio;

    @Schema(description = "达标得分(原始得分)")
    private BigDecimal score;

    @Schema(description = "达标得分(五分制)")
    private BigDecimal scoreFive;

    @Schema(description = "达标得分(十分制)")
    private BigDecimal scoreTen;

    @Schema(description = "达标率")
    private Integer compliancerate;

    @Schema(description = "综合判断的规则")
    private SpRuleBean judgeRule;

    /**
     * 前端apass一个字段搞不定分制，所以加了三个字段
     */
    public void setScore(BigDecimal score) {
        this.score = score;
        this.scoreFive = score;
        this.scoreTen = score;
    }

}
