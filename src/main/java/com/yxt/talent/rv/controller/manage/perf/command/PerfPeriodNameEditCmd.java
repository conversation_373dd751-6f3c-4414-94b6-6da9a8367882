package com.yxt.talent.rv.controller.manage.perf.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "绩效周期名称编辑传值对象")
public class PerfPeriodNameEditCmd implements Command {

    @Schema(description = "绩效周期主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "绩效周期ID不能为空")
    private String id;

    @Schema(description = "绩效周期名称,长度最多为50", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "apis.sptalentrv.prj.period.name.not.null")
    @Size(max = 50, min = 1, message = "apis.sptalentrv.prj.period.name.size.out.limit")
    private String periodName;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;
}
