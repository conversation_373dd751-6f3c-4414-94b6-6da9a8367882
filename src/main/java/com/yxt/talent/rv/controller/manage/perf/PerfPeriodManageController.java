package com.yxt.talent.rv.controller.manage.perf;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.perf.lagecy.PerfPeriodAppService;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodCmd;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodNameEditCmd;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodSortCmd;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodIdNameVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.infrastructure.common.constant.AuthCodes;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.PERF_CYCLE_SET;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "绩效周期管理", description = "绩效周期管理")
@RequestMapping(value = "/mgr/period")
public class PerfPeriodManageController {
    private final AuthService authService;
    private final PerfPeriodAppService perfPeriodAppService;

    @Operation(summary = "绩效周期列表")
    @GetMapping(value = "")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public List<PerfPeriodVO> list(HttpServletRequest request) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return perfPeriodAppService.findList(userCache.getOrgId());
    }

    @Operation(summary = "绩效周期新增")
    @Parameters({@Parameter(name = "performancePeriod4Create", description = "绩效周期传值对象")})
    @PostMapping(value = "", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(type = {AuthType.TOKEN}, codes = {AuthCodes.PERF_CYCLE_SET})
    @Auditing
    //@AuditingPlus(strategyClass = PerfPeriodAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.PERF_PERIOD_ADD, paramExp = "#perfPeriodCmd")
    public PerfPeriodIdNameVO add(
            HttpServletRequest request,
            @Valid @RequestBody PerfPeriodCmd perfPeriodCmd) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return perfPeriodAppService.addPerfPeriod(
                userCache.getOrgId(), userCache.getUserId(), perfPeriodCmd);
    }

    @Operation(summary = "绩效周期编辑")
    @Parameters({@Parameter(name = "performancePeriod4Create", description = "绩效周期传值对象")})
    @PutMapping(value = "", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {PERF_CYCLE_SET})
    @Auditing
    //@AuditingPlus(strategyClass = PerfPeriodAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.PERF_PERIOD_UPDATE, paramExp = "#perfPeriodCmd")
    public void edit(
            HttpServletRequest request,
            @Valid @RequestBody PerfPeriodCmd perfPeriodCmd) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfPeriodAppService.editPerfPeriod(
                userCache.getOrgId(), userCache.getUserId(), perfPeriodCmd);
    }

    @Operation(summary = "绩效周期名称编辑（用于被盘点项目使用的绩效周期）")
    @Parameters({@Parameter(name = "perfPeriodNameEditCmd", description = "绩效周期名称编辑传值对象")})
    @PutMapping(value = "/name", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {PERF_CYCLE_SET})
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.PERF_PERIOD_UPDATE, paramExp = "#perfPeriodNameEditCmd")
    public void editName(
            HttpServletRequest request,
            @Valid @RequestBody PerfPeriodNameEditCmd perfPeriodNameEditCmd) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfPeriodAppService.editPerfPeriodName(
                userCache.getOrgId(), userCache.getUserId(), perfPeriodNameEditCmd);
    }

    @Operation(summary = "绩效周期拖拽排序")
    @PostMapping(value = "/sort", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {PERF_CYCLE_SET})
    public void sort(
            HttpServletRequest request,
            @Valid @RequestBody PerfPeriodSortCmd periodSort) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfPeriodAppService.sort(userCache.getOrgId(), userCache.getUserId(), periodSort);
    }

    @Operation(summary = "删除绩效周期")
    @Parameters({@Parameter(name = "id", description = "绩效周期id", in = ParameterIn.PATH, required = true)})
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {PERF_CYCLE_SET})
    @Auditing
    //@AuditingPlus(strategyClass = PerfPeriodDelAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.PERF_PERIOD_DELETE, paramExp = "#id")
    public void delete(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfPeriodAppService.removePeriod(userCache.getOrgId(), id);
    }
}
