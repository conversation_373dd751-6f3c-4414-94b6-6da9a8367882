package com.yxt.talent.rv.application.xpd.rule;

import com.alibaba.fastjson.JSON;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.application.xpd.aom.XpdAomService;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtBO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimCalcDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimLevelRuleDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimPerfResultDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleCalcInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleCalcRefDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdIndicatorCalcBean;
import com.yxt.talent.rv.application.xpd.common.dto.XpdLevelRuleDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleCalcDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleCalcSdDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleInDto;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleCalcMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleCalcDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleCalcIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.XpdCalcTypeEnum.*;
import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.XpdResultTypeEnum.*;

/**
 * 项目配置的基础类
 *
 * <AUTHOR>
 * @date 2024/12/20 14:47
 */
@Slf4j
public class XpdRuleBaseService<T> {

    @Resource
    protected XpdMapper xpdMapper;
    @Resource
    protected XpdRuleConfMapper xpdRuleConfMapper;
    @Resource
    protected XpdRuleMapper xpdRuleMapper;
    @Resource
    protected XpdDimMapper xpdDimMapper;
    @Resource
    protected XpdLevelMapper xpdLevelMapper;
    @Resource
    protected XpdDimRuleMapper xpdDimRuleMapper;
    @Resource
    protected XpdRuleCalcDimMapper xpdRuleCalcDimMapper;
    @Resource
    protected XpdRuleCalcIndicatorMapper xpdRuleCalcIndicatorMapper;
    @Resource
    protected XpdDimRuleCalcMapper xpdDimRuleCalcMapper;
    @Resource
    protected XpdImportMapper xpdImportMapper;
    @Resource
    protected XpdResultCalcService xpdResultCalcService;
    @Resource
    protected XpdAomService xpdAomService;
    @Resource
    protected PerfActivityService perfActivityService;
    @Resource
    protected ApplicationContext applicationContext;
    @Resource
    private SpRuleService spRuleService;

    protected static final String RULE_CALC_EMPTY = "暂未配置计算规则";
    protected static final BigDecimal HUNDRED = new BigDecimal(100);
    protected static final String SD_DIM_ID_ORIGIN = "%s_" + AppConstants.ORIGIN;

    @SuppressWarnings("unchecked")
    public T getSelfProxy() {
        return (T) applicationContext.getBean(this.getClass());
    }


    protected XpdPO checkXpdExist(String xpdId) {
        XpdPO xpd = xpdMapper.selectById(xpdId);
        Validate.isTrue(xpd != null, ExceptionKeys.XPD_NOT_EXIST);
        return xpd;
    }

    protected XpdRulePO checkXpdRuleExist(String xpdRuleId) {
        XpdRulePO xpdRule = xpdRuleMapper.selectById(xpdRuleId);
        Validate.isTrue(xpdRule != null, ExceptionKeys.XPD_RULE_NOT_EXIST);
        return xpdRule;
    }

    protected XpdRulePO checkXpdRuleExistByXpdId(String orgId, String xpdId) {
        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, xpdId);
        Validate.isTrue(xpdRule != null, ExceptionKeys.XPD_RULE_NOT_EXIST);
        return xpdRule;
    }

    protected XpdRuleConfPO checkXpdRuleConfExistByXpdId(String orgId, String xpdId) {
        // 规则数据
        XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        Validate.isTrue(ruleConf != null, ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        return ruleConf;
    }

    protected XpdRuleConfPO checkXpdRuleConfExist(String ruleConfId) {
        // 规则数据
        XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectById(ruleConfId);
        Validate.isTrue(ruleConf != null, ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        return ruleConf;
    }

    protected XpdDimRulePO checkXpdDimRuleExist(String xpdRuleId) {
        XpdDimRulePO dimRule = xpdDimRuleMapper.selectById(xpdRuleId);
        Validate.isTrue(dimRule != null, ExceptionKeys.XPD_DIM_RULE_NOT_EXIST);
        return dimRule;
    }

    protected XpdDimPO checkXpdDimExistBySdDimId(String orgId, String xpdId, String sdDimId) {
        XpdDimPO dim = xpdDimMapper.selectByXpdIdAndSdDimId(orgId, xpdId, sdDimId);
        Validate.isTrue(dim != null, ExceptionKeys.XPD_DIM_NOT_EXIST);
        return dim;
    }

    /**
     * 维度是否被导入维度结果
     *
     * @param sdDimId 维度ID
     * @return true：导入 false：未导入
     */
    protected boolean dimIsImport(String orgId, String xpdId, String sdDimId) {
        List<XpdImportPO> imports = xpdImportMapper.listBySdDimIdsAndImportType(orgId, xpdId, ListUtil.singletonList(sdDimId), XpdImportTypeEnum.DIM.getCode());
        return CollectionUtils.isNotEmpty(imports);
    }

    protected List<XpdDimLevelRuleDto> convert2LevelRuleList(XpdDimRulePO dimRule, Map<String, XpdGridLevelPO> gridLevelMap) {
        List<XpdDimLevelRuleDto> levelRules = BeanCopierUtil.convertList(JSON.parseArray(dimRule.getLevelRule(), DimGridLevelRuleDTO.class),
                dimGridLevel -> {
                    XpdDimLevelRuleDto dimLevelRuleDto = new XpdDimLevelRuleDto();
                    BeanCopierUtil.copy(dimGridLevel, dimLevelRuleDto);
                    if (gridLevelMap.containsKey(dimLevelRuleDto.getGridLevelId())) {
                        dimLevelRuleDto.setLevelName(gridLevelMap.get(dimLevelRuleDto.getGridLevelId()).getLevelName());
                    }
                    return dimLevelRuleDto;
                });
        RuleMainBase mainData = new RuleMainBase();
        mainData.setOrgId(dimRule.getOrgId());
        mainData.setBizId(dimRule.getXpdId());
        mainData.setLocale(YxtBasicUtils.requestLocale());
        spRuleService.calcRuleDisplay(mainData, levelRules,
            XpdDimLevelRuleDto::getJudgeRule, XpdDimLevelRuleDto::setJudgeRuleDisplay);
        if (DimCalcTypeEnum.byPerfResult(dimRule.getCalcType())) {
            Map<String, XpdDimPerfResultDto> perfResultMap = StreamUtil.list2map(getPerfResults(dimRule.getOrgId(), dimRule.getXpdId(), dimRule.getAomActId()),
                    XpdDimPerfResultDto::getId);
            for (XpdDimLevelRuleDto levelRule : levelRules) {
                if (CollectionUtils.isNotEmpty(levelRule.getMatchValues())) {
                    List<XpdDimPerfResultDto> resultDtoList = Lists.newArrayList();
                    for (String matchValue : levelRule.getMatchValues()) {
                        XpdDimPerfResultDto perfResultDto = new XpdDimPerfResultDto();
                        perfResultDto.setId(matchValue);
                        perfResultDto.setName(perfResultMap.getOrDefault(matchValue, new XpdDimPerfResultDto()).getName());
                        resultDtoList.add(perfResultDto);
                    }
                    levelRule.setMatchValueList(resultDtoList);
                }
            }
        }
        return levelRules;
    }

    /**
     * 获取绩效活动的结果列表
     *
     * @param orgId      机构ID
     * @param xpdId      项目ID
     * @param actvPerfId 绩效活动ID
     * @return list
     */
    public List<XpdDimPerfResultDto> getPerfResults(String orgId, String xpdId, String actvPerfId) {
        List<ActivityPerfResultConfPO> resultConfs = xpdAomService.listPerfConfs(orgId, actvPerfId);
        return BeanCopierUtil.convertList(resultConfs, resultConf -> {
            XpdDimPerfResultDto resultDto = new XpdDimPerfResultDto();
            resultDto.setId(resultConf.getId());
            resultDto.setName(resultConf.getResultName());
            return resultDto;
        });
    }

    /**
     * 获取项目的分层规则
     *
     * @param orgId     机构ID
     * @param xpdRuleId 项目规则ID
     * @return XpdLevelRuleDto.class
     */
    protected List<XpdLevelPO> getXpdLevelRules(String orgId, String xpdRuleId) {
        return xpdLevelMapper.listByXpdRuleId(orgId, xpdRuleId);
    }

    /**
     * 获取项目的分层规则
     *
     * @return XpdLevelRuleDto.class
     */
    protected List<XpdLevelRuleDto> convert2XpdLevelRules(List<XpdLevelPO> levelList, Integer resultType) {
        List<XpdLevelRuleDto> levelRuleDtoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(levelList)) {
            return new ArrayList<>();
        }
        levelList.forEach(level -> {
            XpdLevelRuleDto levelRuleDto = new XpdLevelRuleDto();
            BeanCopierUtil.copy(level, levelRuleDto);
            levelRuleDto.setLevelId(level.getId());
            levelRuleDto.setSpRuleBean(JSON.parseObject(level.getFormula(), SpRuleBean.class));
            levelRuleDtoList.add(levelRuleDto);
        });
        if (!XpdResultTypeEnum.byDimLevelResult(resultType)) {
            levelRuleDtoList.forEach(item -> {
                item.setJudgeRule(item.getSpRuleBean());
                item.setSpRuleBean(null);
            });
        }
        return levelRuleDtoList;
    }

    /**
     * 获取维度及其配置的子维度的总分
     *
     * @param dimCalcs 需要计算的维度
     */
    public Map<String, BigDecimal> getTotalScore4Dims(XpdPO xpd, String sdDimId, List<XpdDimCalcDto> dimCalcs,
                                                      Map<String, XpdDimRulePO> dimRuleMap,
                                                      Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                                      Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap) {

        Map<String, BigDecimal> totalScoreMap = new HashMap<>();
        BigDecimal totalScore = null;
        for (XpdDimCalcDto dimCalcDto : dimCalcs) {
            calcDimValue(dimCalcDto, xpd, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
            if (dimCalcDto.getTotalScore() != null) {
                totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                totalScore = totalScore.add(dimCalcDto.getTotalScore());
            }
        }
        totalScoreMap.put(sdDimId, totalScore);
        return totalScoreMap;
    }

    /**
     * 维度规则更新时的总分
     */
    public BigDecimal getTotalScore4DimUp(String orgId, String sdDimId, XpdPO xpd, Integer calcType,
                                          Map<String, XpdDimRulePO> dimRuleMap,
                                          Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                          List<XpdDimRuleCalcInDto> dimCalcList) {

        // 计算规则：按子维度结果计算
        if (DimCalcTypeEnum.bySubDimension(calcType)) {
            Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap =
                    xpdDimRuleCalcMapper.listByXpdId(orgId, xpd.getId()).stream().collect(Collectors.groupingBy(XpdDimRuleCalcPO::getSdDimId));
            List<XpdDimCalcDto> dimCalcs = new ArrayList<>();
            for (XpdDimRuleCalcInDto calcInDto : dimCalcList) {
                XpdDimCalcDto dimCalcDto = new XpdDimCalcDto();
                dimCalcDto.setSdDimId(calcInDto.getSdDtos().get(0).getSdId());
                dimCalcDto.setWeight(calcInDto.getWeight());
                dimCalcs.add(dimCalcDto);
            }
            Map<String, BigDecimal> totalScoreMap = getTotalScore4Dims(xpd, sdDimId, dimCalcs, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap);
            return totalScoreMap.get(sdDimId);
        } else {
            // 计算规则：按指标结果计算
            List<XpdRuleCalcDto> calcDtos = BeanCopierUtil.convertList(dimCalcList, dimCalc -> {
                XpdRuleCalcDto calcDto = new XpdRuleCalcDto();
                calcDto.setSdDtos(dimCalc.getSdDtos());
                calcDto.setCalcMethod(dimCalc.getCalcMethod());
                calcDto.setWeight(dimCalc.getWeight());
                calcDto.setRefIds(dimCalc.getRefIds());
                return calcDto;
            });
            return getTotalScoreByIndicators(orgId, xpd, calcDtos);
        }
    }

    public BigDecimal getTotalScoreByIndicators(String orgId, XpdPO xpd, List<XpdRuleCalcDto> ruleCalcList) {
        List<XpdImportPO> importIndicators = xpdImportMapper.findByXpdIdAndImportType(orgId, xpd.getId(), XpdImportTypeEnum.DIM_INDICATOR.getCode(), false);
        List<XpdIndicatorCalcBean> calcDtoList = BeanCopierUtil.convertList(ruleCalcList, indicator -> {
            XpdIndicatorCalcBean calcDto = new XpdIndicatorCalcBean();
            calcDto.setSdIndicatorId(indicator.getSdDtos().get(0).getSdId());
            calcDto.setCalcMethod(indicator.getCalcMethod());
            calcDto.setWeight(indicator.getWeight());
            calcDto.setRefList(convertRefIds(indicator.getRefIds(), importIndicators));
            return calcDto;
        });
        calcIndicatorWeightedTotalScoreByScoreSystem(xpd, calcDtoList);
        return calcTotalValueByIndicator(calcDtoList);
    }

    public BigDecimal getTotalScore4XpdUp(String orgId,
                                          XpdPO xpd,
                                          XpdRuleInDto inDto) {
        // 结果类型：得分 && 分层方式：固定值
        if (byScore(inDto.getResultType()) && XpdLevelTypeEnum.byFixedValue(inDto.getLevelType())) {
            // 计算规则：指标&&维度
            List<XpdRuleCalcDto> ruleCalcList = inDto.getRuleCalcList();
            // 计算方式：按指标结果计算
            if (byIndicator(inDto.getCalcType())) {
                return getTotalScoreByIndicators(orgId, xpd, ruleCalcList);
            } else {
                // 计算方式：按维度结果计算
                BigDecimal totalScore = null;
                Map<String, BigDecimal> totalScoreMap = new HashMap<>();
                List<XpdDimRulePO> dimRules = xpdDimRuleMapper.listByXpdId(orgId, xpd.getId());
                Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRules);
                Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRules);
                Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap =
                        xpdDimRuleCalcMapper.listByXpdId(orgId, xpd.getId()).stream().collect(Collectors.groupingBy(XpdDimRuleCalcPO::getSdDimId));
                for (XpdRuleCalcDto ruleCalcDto : ruleCalcList) {
                    calcNonPerfDimTotalScore(xpd, ruleCalcDto, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
                    if (ruleCalcDto.getTotalScore() != null) {
                        if (totalScore == null) {
                            totalScore = BigDecimal.ZERO;
                        }
                        // 项目总分
                        totalScore = totalScore.add(ruleCalcDto.getTotalScore());
                    }
                }
                return totalScore;
            }
        }
        return null;
    }

    /**
     * 获取项目的得分及维度的分值
     *
     * @param needCalcRemainingDims 是否需要计算不在项目计算规则中，但在项目盘点中维度的维度分
     * @param indicatorCalcList     返回值，用得到 项目带指标分的指标列表
     */
    public Map<String, BigDecimal> calcXpdTotalScoreAndDimScores(String orgId,
                                                        XpdPO xpd,
                                                        XpdRulePO xpdRule,
                                                        boolean needCalcRemainingDims,
                                                        List<XpdIndicatorCalcBean> indicatorCalcList) {
        Map<String, BigDecimal> totalScoreMap = new HashMap<>();
        String xpdId = xpd.getId();
        List<XpdDimPO> xpdDims = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(xpdDims)) {
            return new HashMap<>();
        }
        List<String> sdDimIds = StreamUtil.mapList(xpdDims, XpdDimPO::getSdDimId);
        Map<String, XpdDimPO> xpdDimMap = StreamUtil.list2map(xpdDims, XpdDimPO::getSdDimId);

        List<XpdDimRulePO> dimRules = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRules);
        Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRules);
        Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap =
                xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId).stream().collect(Collectors.groupingBy(XpdDimRuleCalcPO::getSdDimId));

        if (byIndicator(xpdRule.getCalcType())) {
            // 计算方式：按指标结果计算
            calcXpdRuleTotalScoreByIndicators(orgId, xpd, xpdRule, indicatorCalcList, totalScoreMap);
        } else {
            // 计算方式：按维度结果计算
            List<XpdRuleCalcDimPO> calcDims = xpdRuleCalcDimMapper.listByXpdRuleId(orgId, xpdRule.getId());
            calcXpdRuleTotalScoreByDims(xpd, calcDims, xpdDimMap, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
            List<String> calcSdDimIds = StreamUtil.mapList(calcDims, XpdRuleCalcDimPO::getSdDimId);
            sdDimIds = ListUtil.difference(sdDimIds, calcSdDimIds);
        }

        if (needCalcRemainingDims) {
            // 计算剩下维度的维度分 为啥要计算？因为详细规则的校验要用到
            List<XpdRuleCalcDto> ruleCalcs = Lists.newArrayList();
            for (String sdDimId : sdDimIds) {
                if (dimRuleMap.containsKey(sdDimId)) {
                    XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
                    XpdRuleCalcDto ruleCalcDto = new XpdRuleCalcDto();
                    XpdRuleCalcSdDto sdDto = new XpdRuleCalcSdDto();
                    sdDto.setSdId(dimRule.getSdDimId());
                    ruleCalcDto.setSdDtos(Lists.newArrayList(sdDto));
                    ruleCalcDto.setWeight(dimRule.getWeight());
                    ruleCalcs.add(ruleCalcDto);
                }
            }
            for (XpdRuleCalcDto ruleCalcDto : ruleCalcs) {
                calcNonPerfDimTotalScore(xpd, ruleCalcDto, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
            }
        }

        return totalScoreMap;
    }

    /**
     * 项目规则计算方式：按指标结果计算
     * 计算项目的总分
     *
     * @param indicatorCalcList 返回值，用得到 项目带指标分的指标列表
     */
    protected void calcXpdRuleTotalScoreByIndicators(String orgId,
                                       XpdPO xpd,
                                       XpdRulePO xpdRule,
                                       List<XpdIndicatorCalcBean> indicatorCalcList,
                                       Map<String, BigDecimal> totalScoreMap) {

        if (XpdCalcRuleEnum.isNormal(xpdRule.getCalcRule())) {
            List<XpdRuleCalcIndicatorPO> indicators = xpdRuleCalcIndicatorMapper.listByXpdRuleId(orgId, xpdRule.getId());
            List<XpdIndicatorCalcBean> calcDtoList = BeanCopierUtil.convertList(indicators, indicator -> {
                XpdIndicatorCalcBean calcDto = new XpdIndicatorCalcBean();
                BeanCopierUtil.copy(indicator, calcDto);
                calcDto.setRefList(JSON.parseArray(indicator.getRefIds(), XpdDimRuleCalcRefDto.class));
                return calcDto;
            });

            if (byScore(xpdRule.getResultType())) {
                // 计算加权之后的指标总分数
                calcIndicatorWeightedTotalScoreByScoreSystem(xpd, calcDtoList);
                // 根据每个指标加权分数计算项目规则整体的总分数
                BigDecimal totalScore = calcTotalValueByIndicator(calcDtoList);
                totalScoreMap.put(xpd.getId(), totalScore);
            }
            indicatorCalcList.addAll(calcDtoList);
        }
    }

    /**
     * 项目规则计算方式：按维度结果计算
     * 计算维度和项目的总分
     *
     * @param calcDims      需要参与计算的维度
     * @param totalScoreMap 返回值 维度分和指标分map
     */
    protected void calcXpdRuleTotalScoreByDims(XpdPO xpd,
                                 List<XpdRuleCalcDimPO> calcDims,
                                 Map<String, XpdDimPO> xpdDimMap,
                                 Map<String, XpdDimRulePO> dimRuleMap,
                                 Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                 Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap,
                                 Map<String, BigDecimal> totalScoreMap) {

        List<XpdRuleCalcDto> ruleCalcs = BeanCopierUtil.convertList(calcDims, calcDim -> {
            XpdRuleCalcDto ruleCalcDto = new XpdRuleCalcDto();
            XpdRuleCalcSdDto sdDto = new XpdRuleCalcSdDto();
            sdDto.setSdId(calcDim.getSdDimId());
            ruleCalcDto.setSdDtos(Lists.newArrayList(sdDto));
            ruleCalcDto.setWeight(calcDim.getWeight());
            return ruleCalcDto;
        });

        BigDecimal totalScore = null;
        for (XpdRuleCalcDto ruleCalcDto : ruleCalcs) {
            String sdDimId = ruleCalcDto.getSdDtos().get(0).getSdId();
            XpdDimPO xpdDim = xpdDimMap.get(sdDimId);
            if (xpdDim != null) {
                // 绩效维度
                if (DimTypeEnum.isPerf(xpdDim.getDimType())) {
                    XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
                    if (dimRule != null) {
                        totalScore = calcDimPerfWeightedTotalScore(xpd, dimRule.getAomActId(), ruleCalcDto.getWeight());
                        ruleCalcDto.setTotalScore(totalScore);
                        totalScoreMap.put(sdDimId, totalScore);
                    }
                } else {
                    // 非绩效维度
                    calcNonPerfDimTotalScore(xpd, ruleCalcDto, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
                }
            }

            if (ruleCalcDto.getTotalScore() != null) {
                totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                // 项目总分
                totalScore = totalScore.add(ruleCalcDto.getTotalScore());
            }
        }
        totalScoreMap.put(xpd.getId(), totalScore);
    }

    /**
     * 计算非绩效维度总分
     * @param xpd
     * @param ruleCalcDto
     * @param dimRuleMap
     * @param dimRuleParentIdMap
     * @param dimRuleCalcsMap
     * @param totalScoreMap
     */
    protected void calcNonPerfDimTotalScore(XpdPO xpd,
                                XpdRuleCalcDto ruleCalcDto,
                                Map<String, XpdDimRulePO> dimRuleMap,
                                Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap,
                                Map<String, BigDecimal> totalScoreMap) {

        // 配了指标,一定是末级维度
        String sdDimId = ruleCalcDto.getSdDtos().get(0).getSdId();
        if (dimRuleCalcsMap.containsKey(sdDimId)) {
            List<XpdDimRuleCalcPO> dimRuleCalcs = dimRuleCalcsMap.get(sdDimId);
            // 计算该维度下各个指标的加权总分
            List<XpdIndicatorCalcBean> calcDtoList = calcIndicatorCalcWeightedScore(xpd, dimRuleCalcs);
            // 维度的分数
            BigDecimal totalScore = calcTotalValueByIndicator(calcDtoList);
            totalScoreMap.put(String.format(SD_DIM_ID_ORIGIN, sdDimId), totalScore);
            // 加上权重
            totalScore = calcWeightedTotalScore(totalScore, ruleCalcDto.getWeight());
            ruleCalcDto.setTotalScore(totalScore);
            totalScoreMap.put(sdDimId, totalScore);
            return;
        }

        // 没有配置指标,但配置了维度规则
        if (dimRuleMap.containsKey(sdDimId)) {
            XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
            List<XpdDimRulePO> children = dimRuleParentIdMap.get(dimRule.getId());
            // 配置了子维度计算规则
            if (CollectionUtils.isNotEmpty(children)) {
                List<XpdDimCalcDto> dimCalcDtos = BeanCopierUtil.convertList(children, child -> {
                    XpdDimCalcDto dimCalcDto = new XpdDimCalcDto();
                    dimCalcDto.setSdDimId(child.getSdDimId());
                    dimCalcDto.setWeight(child.getWeight());
                    return dimCalcDto;
                });
                ruleCalcDto.setDimCalcList(dimCalcDtos);

                BigDecimal totalScore = null;
                for (XpdDimCalcDto subDimCalcDto : dimCalcDtos) {
                    calcDimValue(subDimCalcDto, xpd, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
                    if (subDimCalcDto.getTotalScore() != null) {
                        totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                        totalScore = totalScore.add(subDimCalcDto.getTotalScore());
                    }
                }
                totalScoreMap.put(String.format(SD_DIM_ID_ORIGIN, sdDimId), totalScore);
                totalScore = calcWeightedTotalScore(totalScore, ruleCalcDto.getWeight());
                totalScoreMap.put(sdDimId, totalScore);
                ruleCalcDto.setTotalScore(totalScore);
            }
        }
    }

    /**
     * 计算绩效维度的加权得分
     *
     * @param refId 绩效活动的ID
     * @return 维度分
     */
    protected BigDecimal calcDimPerfWeightedTotalScore(XpdPO xpd, String refId, BigDecimal weight) {
        String orgId = xpd.getOrgId();
        XpdRuleConfPO xpdRuleConfPO = checkXpdRuleConfExistByXpdId(orgId, xpd.getId());
        BigDecimal originTotalScore = perfActivityService.getTotalScore(orgId, refId);
        originTotalScore = Optional.ofNullable(originTotalScore).orElse(BigDecimal.ZERO);
        BigDecimal totalScore = ScoreSystemEnum.getMaxScore(xpdRuleConfPO.getScoreSystem(), originTotalScore);
        log.debug("LOG20903:计算绩效维度的得分, 项目ID={}, 绩效活动ID={}, 原始总分={}, 转换分制之后的总分={}", xpd.getId(), refId, originTotalScore, totalScore);
        return calcWeightedTotalScore(originTotalScore, weight);
    }

    /**
     * 计算维度的维度分
     */
    protected void calcDimValue(XpdDimCalcDto dimCalcDto,
                                XpdPO xpd,
                                Map<String, XpdDimRulePO> dimRuleMap,
                                Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap,
                                Map<String, BigDecimal> totalScoreMap) {

        String sdDimId = dimCalcDto.getSdDimId();
        if (dimRuleCalcsMap.containsKey(sdDimId)) {
            List<XpdDimRuleCalcPO> indicators = dimRuleCalcsMap.get(sdDimId);
            List<XpdIndicatorCalcBean> calcDtoList = calcIndicatorCalcWeightedScore(xpd, indicators);
            BigDecimal totalScore = calcTotalValueByIndicator(calcDtoList);
            totalScoreMap.put(String.format(SD_DIM_ID_ORIGIN, sdDimId), totalScore);
            // 维度的分数
            totalScore = calcWeightedTotalScore(totalScore, dimCalcDto.getWeight());
            dimCalcDto.setTotalScore(totalScore);
            totalScoreMap.put(sdDimId, totalScore);
            return;
        }

        // 没有配置指标,但配置了维度规则
        if (dimRuleMap.containsKey(sdDimId)) {
            XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
            List<XpdDimRulePO> children = dimRuleParentIdMap.get(dimRule.getId());
            // 配置了子维度计算规则
            if (CollectionUtils.isNotEmpty(children)) {
                List<XpdDimCalcDto> subDimCalcDtos = BeanCopierUtil.convertList(children, child -> {
                    XpdDimCalcDto subDimCalDto = new XpdDimCalcDto();
                    subDimCalDto.setSdDimId(child.getSdDimId());
                    subDimCalDto.setWeight(child.getWeight());
                    return subDimCalDto;
                });
                dimCalcDto.setChildren(subDimCalcDtos);

                BigDecimal totalScore = null;
                for (XpdDimCalcDto subDimCalcDto : subDimCalcDtos) {
                    calcDimValue(subDimCalcDto, xpd, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
                    if (subDimCalcDto.getTotalScore() != null) {
                        totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                        totalScore = totalScore.add(subDimCalcDto.getTotalScore());
                    }
                }
                totalScoreMap.put(String.format(SD_DIM_ID_ORIGIN, sdDimId), totalScore);
                totalScore = calcWeightedTotalScore(totalScore, dimCalcDto.getWeight());
                dimCalcDto.setTotalScore(totalScore);
                totalScoreMap.put(sdDimId, totalScore);
            }
        }
    }

    protected List<XpdIndicatorCalcBean> calcIndicatorCalcWeightedScore(XpdPO xpd, List<XpdDimRuleCalcPO> calcs) {
        if (CollectionUtils.isNotEmpty(calcs)) {
            List<XpdIndicatorCalcBean> ruleCalcList = BeanCopierUtil.convertList(calcs, ruleCalc -> {
                XpdIndicatorCalcBean calcDto = new XpdIndicatorCalcBean();
                BeanCopierUtil.copy(ruleCalc, calcDto);
                try {
                    calcDto.setRefList(JSON.parseArray(ruleCalc.getRefIds(), XpdDimRuleCalcRefDto.class));
                } catch (Exception e) {
                    log.error("JSON转换出错, refIds: {}", JSON.toJSONString(ruleCalc.getRefIds()));
                }
                return calcDto;
            });
            calcIndicatorWeightedTotalScoreByScoreSystem(xpd, ruleCalcList);
            return ruleCalcList;
        }
        return Lists.newArrayList();
    }

    /**
     * 指标加权之后的总分(已考虑了分制)
     * @param xpd
     * @param calcs
     */
    protected void calcIndicatorWeightedTotalScoreByScoreSystem(XpdPO xpd, List<XpdIndicatorCalcBean> calcs) {
        xpdResultCalcService.calcIndicatorTotalScoreByScoreSystem(xpd, calcs);
        for (XpdIndicatorCalcBean calc : calcs) {
            if (calc.getTotalScore() != null && calc.getWeight() != null) {
                calc.setTotalScore(calc.getTotalScore().multiply(calc.getWeight()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
            }
        }
    }

    protected BigDecimal calcWeightedTotalScoreByDim(XpdDimCalcDto dimCalcDto) {
        if (dimCalcDto.getTotalScore() == null || dimCalcDto.getWeight() == null) {
            return null;
        }
        return calcWeightedTotalScore(dimCalcDto.getTotalScore(), dimCalcDto.getWeight());
    }

    protected BigDecimal calcWeightedTotalScore(BigDecimal totalScore, BigDecimal weight) {
        if (totalScore == null || weight == null) {
            return null;
        }
        return totalScore.multiply(weight).divide(HUNDRED, 2, RoundingMode.HALF_UP);
    }

    protected BigDecimal calcTotalValueByIndicator(List<XpdIndicatorCalcBean> calcDtoList) {
        BigDecimal totalScore = null;
        for (XpdIndicatorCalcBean calcDto : calcDtoList) {
            if (calcDto.getTotalScore() != null) {
                if (totalScore == null) {
                    totalScore = BigDecimal.ZERO;
                }
                totalScore = totalScore.add(calcDto.getTotalScore());
            }
        }
        return totalScore;
    }

    protected Map<String, XpdDimRulePO> getDimRuleMap(String orgId, String xpdId) {
        List<XpdDimRulePO> dimRuleList = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        return getDimRuleMap(dimRuleList);
    }

    protected Map<String, XpdDimRulePO> getDimRuleMap(List<XpdDimRulePO> dimRuleList) {
        return StreamUtil.list2map(dimRuleList, XpdDimRulePO::getSdDimId);
    }

    protected Map<String, List<XpdDimRulePO>> getDimRuleParentIdMap(List<XpdDimRulePO> dimRuleList) {
        dimRuleList.forEach(dimRule -> {
            if (StringUtils.isEmpty(dimRule.getParentId())) {
                dimRule.setParentId(AppConstants.ROOT_ID);
            }
        });
        return dimRuleList.stream().collect(Collectors.groupingBy(XpdDimRulePO::getParentId));
    }

    /**
     * 获取绩效评估活动的扩展字段值
     *
     * @param orgId
     * @param xpd
     * @param aomActvId
     * @return
     */
    protected AomActvExtBO getPerfActvExt(String orgId, XpdPO xpd, String aomActvId) {
        List<AomActvExtBO> perfActvs = xpdAomService.activityIndicatorList(orgId, xpd.getAomPrjId())
                .stream().filter(r -> UacdTypeEnum.ACTV_PERF.getRegId().equals(r.getRefRegId())).collect(Collectors.toList());
        AomActvExtBO perfActv = null;
        for (AomActvExtBO actv : perfActvs) {
            if (aomActvId.equals(actv.getActvRefId())) {
                perfActv = actv;
                break;
            }
        }
        return perfActv;
    }

    protected List<XpdDimRuleCalcRefDto> convertRefIds(List<String> refIds, List<XpdImportPO> imports) {

        if (CollectionUtils.isEmpty(refIds)) {
            return Lists.newArrayList();
        }
        Set<String> importIds = StreamUtil.map2set(imports, XpdImportPO::getId);
        List<XpdDimRuleCalcRefDto> refList = Lists.newArrayList();
        for (String refId : refIds) {
            XpdDimRuleCalcRefDto refDto = new XpdDimRuleCalcRefDto();
            if (String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()).equals(refId)) {
                refDto.setRefId(String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()));
                refDto.setRefType(DimRuleCalcRefEnum.PRI_PROFILE.getCode());
            } else if (importIds.contains(refId)) {
                refDto.setRefId(refId);
                refDto.setRefType(DimRuleCalcRefEnum.IMPORT_DATA.getCode());
            } else {
                refDto.setRefId(refId);
                refDto.setRefType(DimRuleCalcRefEnum.AOM_ACT.getCode());
            }
            refList.add(refDto);
        }
        return refList;
    }
}
