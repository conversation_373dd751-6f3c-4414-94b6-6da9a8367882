package com.yxt.talent.rv.domain.dmp.event;

import com.yxt.event.message.MessageEvent;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.util.List;

@Setter
@Getter
@ToString(callSuper = true)
public class DmpCalcFormUserMessageEvent extends MessageEvent {

    @Serial
    private static final long serialVersionUID = -473698135998128998L;

    /**
     * Dmp.id
     */
    private String projectId;

    /**
     * DmpTask.evalFormId
     */
    private String formId;

    @Nonnull
    private String orgId;

    private List<String> userIds;

    private Integer status;
}
