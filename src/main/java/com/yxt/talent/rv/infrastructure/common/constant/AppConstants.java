package com.yxt.talent.rv.infrastructure.common.constant;

import cn.hutool.core.lang.Pair;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;


@UtilityClass
public final class AppConstants {
    // @formatter:off
    public static final int MINUTE_IN_ONE_HOUR = 60;
    public static final int MINUTE_IN_ONE_DAY = 24 * MINUTE_IN_ONE_HOUR;
    public static final String DB_0_TRANSACTION_MANAGER = "db0TransactionManager";
    public static final String RV_TRANSACTION_MANAGER = DB_0_TRANSACTION_MANAGER;

    /* 可迁移资源code */
    public static final String TRANSFERABLE_RESOURCES_CODE_PRJ = "sprv_inventory";
    public static final String TRANSFERABLE_RESOURCES_CODE_DMP = "sprv_dynamic_inventory";
    public static final String TRANSFERABLE_RESOURCES_CODE_CALI_MEET = "sprv_calibrations";

    public static final Pair<Long, TimeUnit> DEMO_COPY_RUN_DATA_KEEP_TIME = Pair.of(10L, TimeUnit.DAYS);

    /**
     * 代码操作时固定的操用户Id
     */
    public static final String CODE_OPERATOR_ID = "javaapi0-0000-0000-0000-000000000000";

    public static final String SAAS_ORG_ID = "00000000-0000-0000-0000-000000000000";
    public static final String TEMPLATE_XPD_ID = "00000000-0000-0000-0000-000000000000";
    public static final String TEMPLATE_GRID_ID = "00000000-0000-0000-0000-000000000000";

    public static final String SPMODEL_RULE_APP_CODE = "sptalent-jq";


    public static final String ORIGIN = "origin";

    public static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    public static final String DOWNLOAD_MODULE_CODE = "talentRvProject";

    /**
     * 子应用code(ote,udp)
     */
    public static final String DOWNLOAD_APP_CODE = "gwnl";

    public static final String FUNCTION_NAME = "pc_dlc_gwnl_talentProject";

    public static final String ROOT_ID = "0";

    /**
     * 机构级配置id
     */
    public static final String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";


}
